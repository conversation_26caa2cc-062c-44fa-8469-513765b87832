package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetShutdownDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/3/29 15:27
 * @Description:
 */
@Slf4j
@Component
public class PadNetWorkOffTaskExecuteHandler implements ITaskParamExecutorStrategy {
    @Resource
    private PadMapper padMapper;
    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        ContainerNetShutdownDTO shutdownDTO = new ContainerNetShutdownDTO();
        shutdownDTO.setName(padCode);
        Pad pad = padMapper.selectPadByPadCode(padCode);
        shutdownDTO.setStorageId(pad.getNetStorageResId());
        return shutdownDTO;
    }
    public PadNetWorkOffTaskExecuteHandler(NetStorageResUnitService netStorageResUnitService) {
//        this.netStorageResUnitService = netStorageResUnitService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_OFF.getType(), "padNetWorkOffTaskExecuteHandler");
    }
}
