package net.armcloud.paascenter.task.manager.executor.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceReplaceRealAdiTemplateRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpdateAdiTaskQueueBO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.rtc.PadMacLog;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.mapper.PadMacLogMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.task.manager.executor.impl.PadUpgradeImageTaskExecutorStrategy.buildPadOldParam;


/**
 * 替换真机adi模板
 */
@Slf4j
@Component
public class PadReplaceRealAdbTemplateTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final PadTaskMapper padTaskMapper;
    private final PadMacLogMapper padMacLogMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;

    @Override
    public Object execute(TaskQueue taskQueue) {
        return upgradeImageByCMS(taskQueue);
    }

    /**
     * 通过容器管理服务
     */
    private Object upgradeImageByCMS(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadTask padTask = padTaskMapper.getById(taskQueue.getSubTaskId());

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceReplaceRealAdiTemplateRequest.Instance instance = new InstanceReplaceRealAdiTemplateRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        instance.setMac(getAndSetPadMac(padTask.getPadCode()));
        instance.setClearDiskData(padTask.getWipeData());
        InstanceReplaceRealAdiTemplateRequest.ADI adi = new InstanceReplaceRealAdiTemplateRequest.ADI();

        PadUpdateAdiTaskQueueBO padUpgradeImageTaskQueueBO = JSONUtil.toBean(taskQueue.getContentJson(), PadUpdateAdiTaskQueueBO.class);
        instance.setAndroidProp(padUpgradeImageTaskQueueBO.getAndroidProp());
        adi.setTemplateUrl(padUpgradeImageTaskQueueBO.getAdiUrl());
        adi.setTemplatePassword(padUpgradeImageTaskQueueBO.getAdiPassword());
        adi.setLayoutWidth(padUpgradeImageTaskQueueBO.getLayoutWidth());
        adi.setLayoutHigh(padUpgradeImageTaskQueueBO.getLayoutHigh());
        adi.setLayoutDpi(padUpgradeImageTaskQueueBO.getLayoutDpi());
        adi.setLayoutFps(padUpgradeImageTaskQueueBO.getLayoutFps());
        adi.setRealPhoneTemplateId(padUpgradeImageTaskQueueBO.getRealPhoneTemplateId());
        instance.setAdi(adi);

        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));

        InstanceReplaceRealAdiTemplateRequest req = new InstanceReplaceRealAdiTemplateRequest();
        req.setInstances(Lists.newArrayList(instance));
        return req;
    }

    private String getAndSetPadMac(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        String mac = pad.getMac();
        if (StringUtils.isNotBlank(mac)) {
            return mac;
        }

        do {
            mac = MACUtils.generateMacAddress();
            int updateSize = padMapper.updateMacById(pad.getId(), mac);
            if (updateSize <= 0) {
                continue;
            }
            PadMacLog padMacLog = new PadMacLog();
            padMacLog.setPadCode(pad.getPadCode());
            padMacLog.setMac(mac);
            padMacLogMapper.insert(padMacLog);
            return mac;
        } while (true);
    }


    public PadReplaceRealAdbTemplateTaskExecutorStrategy(PadMapper padMapper, PadTaskMapper padTaskMapper, PadMacLogMapper padMacLogMapper,InstanceDetailImageSuccService instanceDetailImageSuccService) {
        this.padMapper = padMapper;
        this.padTaskMapper = padTaskMapper;
        this.padMacLogMapper = padMacLogMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.REPLACE_REAL_ADB.getType(), "padReplaceRealAdbTemplateTaskExecutorStrategy");
    }
}
