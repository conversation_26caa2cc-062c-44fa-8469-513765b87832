package net.armcloud.paascenter.openapi.manager;

import net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.openapi.mapper.AdiCertificateRepositoryMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AdiCertificateManager {
    private final PadMapper padMapper;
    private final AdiCertificateRepositoryMapper adiCertificateRepositoryMapper;

    public AdiCertificateRepository useAdiCertificate(String padCode, String imageParameter) {
        int certificate_level = 4;
        // 镜像参数中是否有指定需要的证书层级
        // 解析 imageParameter，获取证书层级（cert_level），如果没有则默认为4
        if (imageParameter != null && !imageParameter.isEmpty()) {
            try {
                com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(imageParameter);
                if (jsonObject.containsKey("cert_level")) {
                    Integer certLevelFromJson = jsonObject.getInteger("cert_level");
                    if (certLevelFromJson != null) {
                        certificate_level = certLevelFromJson;
                    }
                }
            } catch (Exception e) {
                // 解析异常时，保持默认值
                log.error("useAdiCertificate imageParameter error>>>>", e);
            }
        }

        AdiCertificateRepository adiCertificateRepository = adiCertificateRepositoryMapper.listByRandom(certificate_level, 1).get(0);
        if (adiCertificateRepository == null) {
            return null;
        }

        long adiCertificateRepositoryId = adiCertificateRepository.getId();
        padMapper.updateAdiCertificateIdByPadCode(padCode, adiCertificateRepositoryId);
        adiCertificateRepositoryMapper.incrUseTotal(adiCertificateRepositoryId);
        return adiCertificateRepository;
    }

    public void cancelUseByPadCode(List<String> padCodes) {
        List<Pad> pads = padMapper.listByPadCodes(padCodes);
        if (CollectionUtils.isEmpty(padCodes)) {
            return;
        }

        List<Pad> updatePads = pads.stream()
                .filter(pad -> Optional.ofNullable(pad.getAdiCertificateId()).orElse(0L) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updatePads)) {
            return;
        }

        List<String> updatePadCodes = updatePads.stream().map(Pad::getPadCode).collect(Collectors.toList());
        padMapper.cancelAdiCertificate(updatePadCodes);

        List<Long> adiCertificateIds = updatePads.stream()
                .map(Pad::getAdiCertificateId)
                .filter(adiCertificateId -> Optional.ofNullable(adiCertificateId).orElse(0L) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adiCertificateIds)) {
            return;
        }

        adiCertificateRepositoryMapper.decrUseTotalByIds(adiCertificateIds);
    }

    public AdiCertificateManager(PadMapper padMapper, AdiCertificateRepositoryMapper adiCertificateRepositoryMapper) {
        this.padMapper = padMapper;
        this.adiCertificateRepositoryMapper = adiCertificateRepositoryMapper;
    }
}
