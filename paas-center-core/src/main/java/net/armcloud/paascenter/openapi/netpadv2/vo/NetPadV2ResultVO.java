package net.armcloud.paascenter.openapi.netpadv2.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import org.slf4j.Logger;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.OFFLINE;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.ONLINE;

@Data
@Slf4j
public class NetPadV2ResultVO implements Serializable {

    private final static long serialVersionUID = 1L;

    public NetPadV2ResultVO() {
        this.successList = new ArrayList<>();
        this.failList = new ArrayList<>();
        this.completeList = new ArrayList<>();
        this.failCodeList = new ArrayList<>();
    }

    /**
     * 操作成功的列表（异步回调，代表当前异步执行成功，业务结果需等待回调）
     */
    @ApiModelProperty(value = "操作成功的列表（异步回调，代表当前异步执行成功，业务结果需等待回调）")
    private List<SuccessItem> successList;

    /**
     * 操作失败的列表，由于业务校验不通过或是其他异常导致的无法操作
     */
    @ApiModelProperty(value = "操作失败的列表，由于业务校验不通过或是其他异常导致的无法操作")
    private List<FailItem> failList;

    /**
     * 操作完成的列表，不需要进行异步操作的实例
     */
    @ApiModelProperty(value = "操作完成的列表，不需要进行异步操作的实例")
    private List<CompleteItem> completeList;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private List<String> failCodeList;

    @Data
    private static class SuccessItem {

        @ApiModelProperty(value = "任务id")
        private Integer taskId;

        @ApiModelProperty(value = "实例编号")
        private String padCode;

        @ApiModelProperty(value = "实例在线状态")
        private Integer vmStatus;
    }

    @Data
    private static class FailItem {

        @ApiModelProperty(value = "错误码")
        private Integer errCode;

        @ApiModelProperty(value = "实例编号")
        private String padCode;

        @ApiModelProperty(value = "失败原因")
        private String errMsg;
    }

    @Data
    private static class CompleteItem {

        @ApiModelProperty(value = "实例编号")
        private String padCode;

    }

    public void setCompleteList(List<String> padCodes) {
        if (Objects.isNull(completeList)) {
            completeList = new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(padCodes)) {
            return;
        }
        padCodes.forEach(padCode -> {
            CompleteItem completeItem = new CompleteItem();
            completeItem.setPadCode(padCode);
            completeList.add(completeItem);
        });
    }

    public void setFailList(ExceptionCode exception, List<String> padCodes) {
        if (Objects.isNull(failList)) {
            failList = new ArrayList<>();
        }
        if (Objects.isNull(failCodeList)) {
            failCodeList = new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(padCodes)) {
            return;
        }
        padCodes.forEach(padCode -> {
            FailItem failItem = new FailItem();
            failItem.setPadCode(padCode);
            failItem.setErrMsg(exception.getMsg());
            failItem.setErrCode(exception.getStatus());
            failList.add(failItem);
            failCodeList.add(padCode);
        });
    }

    public void setFailList(Map<ExceptionCode, List<String>> failMap) {
        if (Objects.isNull(failList)) {
            failList = new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(failMap)) {
            return;
        }
        failMap.forEach(this::setFailList);
    }

    public void setSuccessList(PadTaskBO padTaskBO) {
        if (Objects.isNull(successList)) {
            successList = new ArrayList<>();
        }
        if (padTaskBO == null) {
            return;
        }
        if (CollectionUtil.isEmpty(padTaskBO.getSubTasks())) {
            return;
        }
        padTaskBO.getSubTasks().forEach(subTaskBO -> {
            SuccessItem successItem = new SuccessItem();
            successItem.setTaskId(subTaskBO.getCustomerTaskId());
            successItem.setPadCode(subTaskBO.getPadCode());
            successItem.setVmStatus(Boolean.TRUE.equals(subTaskBO.getSendCmdSuccess()) ? ONLINE : OFFLINE);
            if(subTaskBO.getOnline() != null){
                successItem.setVmStatus(subTaskBO.getOnline() ? 1 : 0);
            }
            successList.add(successItem);
        });
    }


    public void printResult(Logger log) {
        log.info("NetPadV2ResultVO result: {}", JSONUtil.toJsonStr(this));
    }
}
